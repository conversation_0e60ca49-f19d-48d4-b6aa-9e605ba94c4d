import { error } from "../../../../../../tool/localize/src/panel"

export const HERO_FOOT_WIDTH = 20 // Hero脚部碰撞宽度（左右各延伸20px）
export enum LandType {
    None = "none",
    Normal = "normal",
    Die = "die",
    Water = "water",
    Rebirth = "rebirth",
}

// Hero轨迹信息
export interface HeroTrajectory {
    // 左脚线段位置
    leftFoot: { start: cc.Vec2, end: cc.Vec2 }
    // 右脚线段位置
    rightFoot: { start: cc.Vec2, end: cc.Vec2 }
    // 世界矩形
    worldPoints: cc.Vec2[]
}

// 碰撞结果
export interface CollisionResult {
    point: cc.Vec2      // 碰撞点
    distance: number    // 碰撞距离（用于选择最近碰撞）
    normal?: cc.Vec2    // 碰撞法线（可选）
}

export interface LandCollider extends cc.Component {
    type: LandType
    node: cc.Node

    intersectWith: (trajectory: HeroTrajectory) => CollisionResult | null

    getLeftMax(): number
    getRightMax(): number
}

class BaseLandCollider extends cc.Component implements LandCollider {
    type: LandType = LandType.None
    node: cc.Node = null

    public intersectWith(_trajectory: HeroTrajectory): CollisionResult | null { return null }

    public getLeftMax(): number { throw new Error("Method not implemented.") }
    public getRightMax(): number { throw new Error("Method not implemented.") }
}

export class NormalLandCollider extends BaseLandCollider {
    type: LandType = LandType.Normal

    private lineNode: cc.Node = null
    private startPos: cc.Vec2
    private endPos: cc.Vec2

    onLoad(): void {
        this.lineNode = this.node.Child("line")
        if (this.lineNode) {
            this.startPos = ut.convertToNodeAR(this.lineNode, this.node.parent.parent, cc.v2(-this.lineNode.anchorX * this.lineNode.width, 0))
            this.endPos = ut.convertToNodeAR(this.lineNode, this.node.parent.parent, cc.v2((1 - this.lineNode.anchorX) * this.lineNode.width, 0))
        }
    }

    public intersectWith(trajectory: HeroTrajectory): CollisionResult | null {
        if (!this.lineNode) {
            console.error(`NormalLandCollider 检测失败,因为不存在可停留线段,name:${this.node.name}`)
            return null
        }
        const intersectPos = cc.v2()
        const trajectories = [trajectory.rightFoot, trajectory.leftFoot]
        for (let i = 0; i < trajectories.length; i++) {
            const traj = trajectories[i]
            if (ut.lineLine(traj.start, traj.end, this.startPos, this.endPos, intersectPos)) {
                if (!intersectPos.fuzzyEquals(traj.start, 0.01)) {
                    intersectPos.x += i == 0 ? -HERO_FOOT_WIDTH : HERO_FOOT_WIDTH
                    return {
                        point: intersectPos,
                        distance: traj.start.sub(intersectPos).mag()
                    }
                }
            }
        }
        return null
    }

    public getLeftMax(): number {
        return this.startPos.x
    }

    public getRightMax(): number {
        return this.endPos.x
    }
}

// 重生地块
export class RebirthLandCollider extends NormalLandCollider {
    type: LandType = LandType.Rebirth

    public intersectWith(trajectory: HeroTrajectory): CollisionResult | null {
        return super.intersectWith(trajectory)
    }
}

// 瀑布地块
export class WaterLandCollider extends NormalLandCollider {
    type: LandType = LandType.Water

    public intersectWith(trajectory: HeroTrajectory): CollisionResult | null {
        return super.intersectWith(trajectory)
    }
}

// 一碰就死地块
export class DieLandCollider extends BaseLandCollider {
    type: LandType = LandType.Die
    worldPoints: cc.Vec2[] = null

    onLoad(): void {
        const collider = this.node.getComponent(cc.PolygonCollider)
        if (!collider) {
            throw new Error(`DieLandCollider 检测失败,因为不存在碰撞器,name:${this.node.name}`)
        }
        this.worldPoints = collider.points.map(point => {
            const localPoint = cc.v2(point.x + collider.offset.x, point.y + collider.offset.y)
            return this.node.convertToWorldSpaceAR(localPoint)
        })
    }

    public intersectWith(trajectory: HeroTrajectory): CollisionResult | null {
        // 首先检查hero的多边形是否与地块多边形相交
        if (cc.Intersection.polygonPolygon(trajectory.worldPoints, this.worldPoints)) {
            return { point: null, distance: 1 }
        }

        // 检查hero的移动轨迹（左脚和右脚线段）是否与地块多边形相交
        const trajectories = [trajectory.leftFoot, trajectory.rightFoot]
        for (let i = 0; i < trajectories.length; i++) {
            const traj = trajectories[i]
            // 检查线段是否与多边形相交
            if (this.lineIntersectsPolygon(traj.start, traj.end, this.worldPoints)) {
                return {
                    point: traj.start,
                    distance: traj.start.sub(traj.end).mag()
                }
            }
        }

        return null
    }

    // 检查线段是否与多边形相交
    private lineIntersectsPolygon(lineStart: cc.Vec2, lineEnd: cc.Vec2, polygonPoints: cc.Vec2[]): boolean {
        // 检查线段的任一端点是否在多边形内
        if (this.pointInPolygon(lineStart, polygonPoints) || this.pointInPolygon(lineEnd, polygonPoints)) {
            return true
        }

        // 检查线段是否与多边形的任一边相交
        for (let i = 0; i < polygonPoints.length; i++) {
            const p1 = polygonPoints[i]
            const p2 = polygonPoints[(i + 1) % polygonPoints.length]

            if (this.lineIntersectsLine(lineStart, lineEnd, p1, p2)) {
                return true
            }
        }

        return false
    }

    // 检查点是否在多边形内（射线法）
    private pointInPolygon(point: cc.Vec2, polygonPoints: cc.Vec2[]): boolean {
        let inside = false
        for (let i = 0, j = polygonPoints.length - 1; i < polygonPoints.length; j = i++) {
            const xi = polygonPoints[i].x, yi = polygonPoints[i].y
            const xj = polygonPoints[j].x, yj = polygonPoints[j].y

            if (((yi > point.y) !== (yj > point.y)) &&
                (point.x < (xj - xi) * (point.y - yi) / (yj - yi) + xi)) {
                inside = !inside
            }
        }
        return inside
    }

    // 检查两条线段是否相交
    private lineIntersectsLine(line1Start: cc.Vec2, line1End: cc.Vec2, line2Start: cc.Vec2, line2End: cc.Vec2): boolean {
        const d1 = this.direction(line2Start, line2End, line1Start)
        const d2 = this.direction(line2Start, line2End, line1End)
        const d3 = this.direction(line1Start, line1End, line2Start)
        const d4 = this.direction(line1Start, line1End, line2End)

        if (((d1 > 0 && d2 < 0) || (d1 < 0 && d2 > 0)) &&
            ((d3 > 0 && d4 < 0) || (d3 < 0 && d4 > 0))) {
            return true
        }

        // 检查共线情况
        if (d1 === 0 && this.onSegment(line2Start, line1Start, line2End)) return true
        if (d2 === 0 && this.onSegment(line2Start, line1End, line2End)) return true
        if (d3 === 0 && this.onSegment(line1Start, line2Start, line1End)) return true
        if (d4 === 0 && this.onSegment(line1Start, line2End, line1End)) return true

        return false
    }

    // 计算向量方向
    private direction(a: cc.Vec2, b: cc.Vec2, c: cc.Vec2): number {
        return (c.x - a.x) * (b.y - a.y) - (b.x - a.x) * (c.y - a.y)
    }

    // 检查点是否在线段上
    private onSegment(p: cc.Vec2, q: cc.Vec2, r: cc.Vec2): boolean {
        return q.x <= Math.max(p.x, r.x) && q.x >= Math.min(p.x, r.x) &&
               q.y <= Math.max(p.y, r.y) && q.y >= Math.min(p.y, r.y)
    }

    public getLeftMax(): number {
        return this.node.x - this.node.width >> 1
    }
    public getRightMax(): number {
        return this.node.x + this.node.width >> 1
    }
}

export class LandColliderFactory {
    private static colliderMap = new Map<LandType, new () => LandCollider>([
        [LandType.Normal, NormalLandCollider],
        [LandType.Die, DieLandCollider],
        [LandType.Water, WaterLandCollider],
        [LandType.Rebirth, RebirthLandCollider],
    ])

    static createCollider(point: cc.Node, index: number, totalCount: number): LandCollider {
        let landType = this.determineLandType(point, index, totalCount)
        const ColliderClass = this.colliderMap.get(landType) || NormalLandCollider
        return point.addComponent(ColliderClass)
    }

    private static determineLandType(point: cc.Node, index: number, totalCount: number): LandType {
        const nodeName = point.name.toLowerCase()
        if (nodeName.includes(LandType.Die)) return LandType.Die
        if (nodeName.includes(LandType.Water)) return LandType.Water
        if (nodeName.includes(LandType.Rebirth)) return LandType.Rebirth

        return LandType.Normal
    }
}
