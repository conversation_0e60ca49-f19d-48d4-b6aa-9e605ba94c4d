import { HeroAction, HeroAnimation, PassengerLifeAnimation, PlanetEvent } from "../../../common/constant/Enums";
import EventType from "../../../common/event/EventType";
import { gameHelper } from "../../../common/helper/GameHelper";
import ActionTree from "../../../model/passenger/ActionTree";
import PlanetEmptyNode from "../../../model/planet/PlanetEmptyNode";
import PlanetWindCtrl from "../../planet/PlanetWindCtrl";
import HeroCmpt from "../hero/HeroCmpt";

import PlanetNodeCmpt from "./PlanetNodeCmpt";

const { ccclass } = cc._decorator;

import { LandCollider, LandColliderFactory, HeroTrajectory, HERO_FOOT_WIDTH, DieLandCollider, NormalLandCollider } from "./JumpGame3Extra"

const MAX_TIME = 2
const MAX_DIS = 1000

type checkRes = { index?: number, pos?: cc.Vec2, dieBreak?: boolean }

@ccclass
export default class JumpGame3Cmpt extends PlanetNodeCmpt {
    model: PlanetEmptyNode = null
    planetCtrl: PlanetWindCtrl = null
    heroNode: cc.Node = null

    powerSp: cc.Sprite = null
    points: cc.Node[] = []
    isJump: boolean = false
    isControl: boolean = false
    touchTime: number = 0

    speed: cc.Vec2 = cc.v2()
    gravity: number = 2000
    minY: number = -500

    prePos: cc.Vec2 = cc.v2()
    nextPos: cc.Vec2 = cc.v2()
    intersectPos: cc.Vec2 = cc.v2()
    resetPos: cc.Vec2 = null
    // 新的碰撞检测系统
    private landColliders: LandCollider[] = []

    actionTree: ActionTree = null

    // 有没有复制下一关的节点过来
    isCopyNext: boolean = false
    // 有没有继承上一关hero的位置过来
    isExtendPrev: boolean = false

    dieNode: cc.Node[] = []

    get curIndex() { return this.model.progress }
    set curIndex(val: number) { this.model.progress = val }

    public listenEventMaps() {
        return [
            { [EventType.PLAENT_CONTROL_TOUCH_START]: this.onTouchStart },
            { [EventType.PLAENT_CONTROL_TOUCH_END]: this.onTouchEnd },
            { [EventType.PLAENT_CONTROL_TOUCH_CANCEL]: this.onTouchEnd },
            { [EventType.PLAENT_CHANGE_JUMP_END]: this.onChange },
            { [EventType.TARGET_PLANET_NODE]: this.onTarget },
        ]
    }

    public async init(model: PlanetEmptyNode, planetCtrl: PlanetWindCtrl) {
        super.init(model)
        this.planetCtrl = planetCtrl
        this.heroNode = planetCtrl.getHeroNode()
        // 用来做碰撞检测
        this.heroNode.width = 100
        this.heroNode.height = 130

        this.listenEventMaps()
        this.powerSp = this.heroNode.Child('ui/power/bar', cc.Sprite)
        this.points = this.node.children

        this.landColliders = this.points.map((point, index) => LandColliderFactory.createCollider(point, index, this.points.length))

        this.actionTree = new ActionTree().init(this)

        const mapNode = this.planetCtrl.getMapNode()
        let curIndex = mapNode.children.indexOf(this.node)
        if (curIndex > 0) {
            const prev = mapNode.children[curIndex - 1]
            if (prev && prev.Component(JumpGame3Cmpt) != null) {
                this.isExtendPrev = true
            }
        }
        this.model.reachOffset = ut.convertToNodeAR(this.points[0], this.node)
        this.model.endOffset = ut.convertToNodeAR(this.points.last(), this.node)

        if (gameHelper.hero.getTargetModel() == this.model) {
            this.onTarget(this.model)
        }
        this.node.zIndex = 0
    }


    async onTarget(model: PlanetEmptyNode) {
        if (this.model != model) return
        // 将下一关卡的第一个节点补充到当前
        const mapNode = this.planetCtrl.getMapNode()
        let curIndex = mapNode.children.indexOf(this.node)
        if (curIndex < mapNode.childrenCount - 1) {
            const next = mapNode.children[curIndex + 1]
            if (next && next.Component(JumpGame3Cmpt) != null && next.childrenCount > 0) {
                const first = next.children[0]
                const to = cc.instantiate(first)
                to.parent = this.node
                to.setPosition(ut.convertToNodeAR(first, this.node))
                this.landColliders.push(LandColliderFactory.createCollider(to, this.points.length - 1, this.points.length))

                // 更新endOffset
                this.model.endOffset = ut.convertToNodeAR(this.points.last(), this.node)
                this.isCopyNext = true
            }
        }

        if (!this.isExtendPrev) {
            const pos = this.getFudaoCenter(this.curIndex)
            gameHelper.hero.setPosition(pos)
        }

        this.planetCtrl.focusHero({ back: true })
        gameHelper.hero.setAction(HeroAction.IDLE)
        this.setControl(true)
        gameHelper.hero.setAction(HeroAction.JUMP_GAME3)
    }

    getFudaoCenter(index: number) {
        const point = this.points[index]
        const line = point.Child("line")
        const startPos = this.node.getPosition()
        const center = ut.convertToNodeAR(line, this.node)
        return cc.v2(startPos.x + center.x, startPos.y + center.y)
    }

    setControl(bol: boolean) {
        this.isControl = bol
        eventCenter.emit(EventType.PLAENT_CAN_CONTROL_JUMP, bol)
    }

    setSpeedByDis(dis: number) {
        let tan = ut.tan(60)
        this.speed.x = Math.sqrt(this.gravity * dis / (2 * tan))
        this.speed.y = this.speed.x * tan
    }

    onChange(x: number) {
        this.setSpeedByDis(x - this.heroNode.x)
    }

    showPower(bol: boolean) {
        this.powerSp.fillRange = 0
        this.powerSp.node.parent.active = bol
    }

    updatePower() {
        if (this.touchTime === 0) return
        let passTime = (Date.now() - this.touchTime) * 0.001
        let per = passTime / MAX_TIME
        if (per > 1) per = 1
        this.powerSp.fillRange = per
    }



    checkReachEnd(dis: number, x: number) {
        if (this.curIndex != this.landColliders.length - 1) return
        const land = this.landColliders.last()
        let start = land.getLeftMax()
        if (dis >= start - x) {
            eventCenter.emit(EventType.PLAENT_CAN_JUMP_TO_END)
        }
    }

    private clampDis(dis) {
        let eventName = this.model.eventName
        if (eventName == PlanetEvent.JUMP_1 || eventName == PlanetEvent.JUMP_2) return dis
        if (this.curIndex != this.landColliders.length - 1) return dis

        const land = this.landColliders.last()
        let maxX = land.getRightMax()
        let maxDis = maxX - this.prePos.x
        return Math.min(dis, maxDis)
    }

    getBirthPointIndex() {
        let index = this.points.slice(0, this.curIndex + 1).findIndex(p => p.name.includes("rebirth"))
        if (index < 0) {
            return 0
        }
        return index
    }

    updatePos(dt: number) {
        let interactInfo: checkRes = null

        let tot = dt
        while (tot) {
            dt = 0.03
            if (tot < dt) {
                dt = tot
            }
            tot -= dt
            this.speed.y -= this.gravity * dt
            this.prePos = this.heroNode.getPosition(this.prePos)
            this.nextPos.x = this.prePos.x + this.speed.x * dt
            this.nextPos.y = this.prePos.y + this.speed.y * dt
            let nextPos = this.nextPos
            interactInfo = this.checkIntersect(nextPos)
            if (interactInfo) {
                break
            }
        }

        let nextPos = this.nextPos
        if (!interactInfo) {
            this.heroNode.setPosition(nextPos)
        }
        else {
            if (!interactInfo.dieBreak) {
                this.curIndex = interactInfo.index
                this.isJump = false
                this.heroNode.setPosition(interactInfo.pos)
                let cmpt = this.heroNode.Component(HeroCmpt)
                let p1 = cmpt.playAnimation(HeroAnimation.JUMP4).then(() => {
                    this.heroNode.Component(HeroCmpt).playAnimation(PassengerLifeAnimation.IDLE, true)
                })
                let p2 = this.checkClaimReward()
                Promise.all([p1, p2]).then(() => {
                    this.model.setProgress(this.curIndex)
                    if (this.curIndex < this.landColliders.length - 1) {
                        this.setControl(true)
                    }
                })
            }
            else {
                // 停止update
                this.isJump = false
                const to = ut.convertToNodeAR(this.planetCtrl.getMapNode(), this.node, nextPos)
                this.dieBreakReset(to)
            }
        }
    }

    checkIntersect(pos: cc.Vec2): checkRes | null {
        const trajectory: HeroTrajectory = {
            leftFoot: {
                start: cc.v2(this.prePos.x - HERO_FOOT_WIDTH, this.prePos.y),
                end: cc.v2(pos.x - HERO_FOOT_WIDTH, pos.y)
            },
            rightFoot: {
                start: cc.v2(this.prePos.x + HERO_FOOT_WIDTH, this.prePos.y),
                end: cc.v2(pos.x + HERO_FOOT_WIDTH, pos.y)
            },
            worldPoints: null
        }

        let closestCollision = null
        let closestDistance = Infinity
        for (let i = 0; i < this.landColliders.length; i++) {
            const cmtp = this.landColliders[i]
            if (!cmtp.node.active) continue
            if (i < this.curIndex) continue
            // hero世界多边形坐标 在需要的时候再计算
            if (cmtp instanceof DieLandCollider && trajectory.worldPoints == null) {
                const heroCollider = this.heroNode.Component(cc.PolygonCollider)
                trajectory.worldPoints = heroCollider.points.map(point => {
                    const localPoint = cc.v2(point.x + heroCollider.offset.x, point.y + heroCollider.offset.y)
                    return this.heroNode.convertToWorldSpaceAR(localPoint)
                })
            }
            const result = cmtp.intersectWith(trajectory)
            if (result && result.distance < closestDistance) {
                closestDistance = result.distance
                closestCollision = { index: i, pos: result.point }
                // 该地块直接返回
                if (cmtp instanceof DieLandCollider) {
                    console.log("die...", pos)
                    return { dieBreak: true }
                }
            }
        }
        return closestCollision
    }

    checkDead() {
        if (this.heroNode.y <= this.minY) {
            return true
        }
    }
    async checkClaimReward() {

    }

    isMain() { return this.model.eventName == PlanetEvent.JUMP_1 || this.model.eventName == PlanetEvent.JUMP_2 }

    reset() {
        this.setControl(true)
        this.isJump = false

        if (this.isMain()) {
            this.heroNode.setPosition(this.resetPos)
        }
        else {
            let pointIndex = this.getBirthPointIndex()
            this.heroNode.setPosition(this.getFudaoCenter(pointIndex))
            this.curIndex = pointIndex
        }
        this.heroNode.Component(HeroCmpt).playAnimation(PassengerLifeAnimation.IDLE, true)
    }

    async dieBreakReset(pos: cc.Vec2) {
        let it: cc.Node = null
        if (this.dieNode.length < 10) {
            const prefab = this.planetCtrl.getHeroJumpDiePrefab()
            it = cc.instantiate2(prefab, this.node)
            it.setPosition(pos)
            this.dieNode.push(it)
            it.addComponent(NormalLandCollider)
        }
        else {
            it = this.dieNode.shift()
            // 播放消失动画
        }
        it.Component(sp.Skeleton).playAnimation("loop", true)
        await ut.wait(5, this)
        this.reset()
    }

    // ============ after are events

    private onTouchStart(_event: cc.Event.EventTouch) {
        if (!this.isControl) return
        this.touchTime = Date.now()
        this.heroNode.Component(HeroCmpt).playAnimation(HeroAnimation.JUMP2, true)
        this.showPower(true)
        gameHelper.hero.setJump()
    }

    private onTouchEnd(_event: cc.Event.EventTouch) {
        if (!this.isControl || this.touchTime <= 0) {
            return
        }
        this.prePos = this.heroNode.getPosition(this.prePos)
        this.resetPos = this.heroNode.getPosition(this.resetPos)
        // 按住多少秒
        let time = (Date.now() - this.touchTime) * 0.001
        time = cc.misc.clampf(time, 0, MAX_TIME)

        let dis = time / MAX_TIME * MAX_DIS
        dis = this.clampDis(dis)
        this.setSpeedByDis(dis)

        this.isJump = true
        this.setControl(false)
        this.touchTime = 0
        this.showPower(false)
        this.heroNode.Component(HeroCmpt).playAnimation(HeroAnimation.JUMP3)
        this.checkReachEnd(dis, this.prePos.x)
    }


    // ======== update

    update(dt: number) {
        super.update(dt)
        if (!this.model) return
        this.updatePower()
        this.actionTree && this.actionTree.update(dt)

        if (!this.isJump) return

        this.updatePos(dt)
        if (this.checkDead()) {
            this.reset()
            return
        }
        if (this.curIndex >= this.landColliders.length - 1) {
            let model = this.model
            this.enabled = false
            gameHelper.hero.setPosition(this.heroNode.getPosition())
            ut.wait(0.5, this).then(async () => {
                this.planetCtrl.focusHero()
                await model.die()
                model.end()
                this.points.pop()
                this.landColliders.pop()
            })
        }
    }

}
